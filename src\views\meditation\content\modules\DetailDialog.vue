<template>
  <el-dialog
    v-model="visible"
    title="内容详情"
    width="800px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="标题" :span="2">
          {{ data.title }}
        </el-descriptions-item>
        <el-descriptions-item label="类型">
          {{ data.type }}
        </el-descriptions-item>
        <el-descriptions-item label="分类">
          {{ data.category }}
        </el-descriptions-item>
        <el-descriptions-item label="时长">
          {{ Math.floor(data.duration / 60) }}分{{ data.duration % 60 }}秒
        </el-descriptions-item>
        <el-descriptions-item label="作者">
          {{ data.author }}
        </el-descriptions-item>
        <el-descriptions-item label="播放次数">
          {{ data.playCount }}
        </el-descriptions-item>
        <el-descriptions-item label="点赞数">
          {{ data.likeCount }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.status === 1 ? 'success' : 'danger'">
            {{ data.status === 1 ? "启用" : "禁用" }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="创建时间">
          {{ data.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="发布时间">
          {{ data.publishTime || "未发布" }}
        </el-descriptions-item>
        <el-descriptions-item label="封面" :span="2">
          <el-image
            :src="data.cover"
            fit="cover"
            style="width: 200px; height: 120px; border-radius: 8px"
            :preview-src-list="[data.cover]"
          />
        </el-descriptions-item>
        <el-descriptions-item label="标签" :span="2">
          <el-tag v-for="tag in data.tags" :key="tag" style="margin-right: 8px">
            {{ tag }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description || "暂无描述" }}
        </el-descriptions-item>
        <el-descriptions-item label="音频链接" :span="2">
          <el-link :href="data.audioUrl" target="_blank" type="primary">
            {{ data.audioUrl }}
          </el-link>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface Content {
  id: number;
  title: string;
  type: string;
  category: string;
  tags: string[];
  duration: number;
  cover: string;
  audioUrl: string;
  description: string;
  author: string;
  playCount: number;
  likeCount: number;
  status: number;

  createTime: string;
  publishTime: string;
}

interface Props {
  modelValue: boolean;
  data?: Content | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, val => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}
</style>
